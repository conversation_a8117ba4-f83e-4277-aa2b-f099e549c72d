import { useCallback, useRef } from 'react';

interface BarcodeDetectionOptions {
  maxTimeBetweenChars?: number;
  minSpeed?: number;
}

interface BarcodeDetectionResult {
  isFromScanner: boolean;
  inputValue: string;
}

export const useBarcodeDetection = (options: BarcodeDetectionOptions = {}) => {
  const { maxTimeBetweenChars = 50, minSpeed = 10 } = options;

  const inputStartTime = useRef<number>(0);
  const lastCharTime = useRef<number>(0);
  const charTimes = useRef<number[]>([]);
  const inputBuffer = useRef<string>('');
  const isTypingFast = useRef<boolean>(false);

  const detectBarcodeInput = useCallback(
    (inputValue: string): BarcodeDetectionResult => {
      const now = Date.now();
      const currentLength = inputValue.length;
      const previousLength = inputBuffer.current.length;

      if (currentLength === 1 || currentLength < previousLength) {
        inputStartTime.current = now;
        lastCharTime.current = now;
        charTimes.current = [now];
        inputBuffer.current = inputValue;
        isTypingFast.current = false;
        return { isFromScanner: false, inputValue };
      }

      if (currentLength > previousLength) {
        const timeSinceLastChar = now - lastCharTime.current;
        charTimes.current.push(now);
        lastCharTime.current = now;
        inputBuffer.current = inputValue;

        // Verifica se o tempo entre caracteres é muito rápido (leitor)
        if (timeSinceLastChar > 0 && timeSinceLastChar <= maxTimeBetweenChars) {
          isTypingFast.current = true;
        } else if (timeSinceLastChar > maxTimeBetweenChars * 3) {
          // Se demorou muito entre caracteres, provavelmente é digitação manual
          isTypingFast.current = false;
        }
      }

      const totalTime = now - inputStartTime.current;
      const avgSpeed = totalTime > 0 ? (currentLength / totalTime) * 1000 : 0;

      const isFromScanner = isTypingFast.current && avgSpeed >= minSpeed;

      return {
        isFromScanner,
        inputValue,
      };
    },
    [maxTimeBetweenChars, minSpeed]
  );

  const resetDetection = useCallback(() => {
    inputStartTime.current = 0;
    lastCharTime.current = 0;
    charTimes.current = [];
    inputBuffer.current = '';
    isTypingFast.current = false;
  }, []);

  return {
    detectBarcodeInput,
    resetDetection,
  };
};
